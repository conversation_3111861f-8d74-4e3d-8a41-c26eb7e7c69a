<?php

  class DbHelper {

    /**
     * Escaping single quotes and converting html-codes of quotes for writing to DB
     *
     * @param string $string
     * @return string
     */
    public static function escape($string) {
      return DBConn::db_link()->real_escape_string($string);
    }

    /**
     * Generate a SQL string for measuring the distance between 2 latitude/longitude points
     * Use as : SELECT DbHelper::getLatLongDistanceQuery($a1, $a2, $b1, $b2) AS distance FROM X
     * or as : SELECT DbHelper::getLatLongDistanceQuery($a1, $a2, 'organisation.lat', 'organisation.lng') AS distance FROM X
     *
     * @param string|float $latitude_1
     * @param string|float $longitude_1
     * @param string|float $latitude_2
     * @param string|float $longitude_2
     * @return string
     */
    public static function getLatLongDistanceQuery($latitude_1, $longitude_1, $latitude_2, $longitude_2) {
      $latitude_1 = DbHelper::escape($latitude_1);
      $longitude_1 = DbHelper::escape($longitude_1);
      $latitude_2 = DbHelper::escape($latitude_2);
      $longitude_2 = DbHelper::escape($longitude_2);

      $distance_query = "(111.1111 * ";
      $distance_query .= "DEGREES(";
      $distance_query .= "ACOS(";
      $distance_query .= "COS(RADIANS(%s))";
      $distance_query .= "* COS(RADIANS(%s))";
      $distance_query .= "* COS(RADIANS(%s) - RADIANS(%s))";
      $distance_query .= "+ SIN(RADIANS(%s))";
      $distance_query .= "* SIN(RADIANS(%s))";
      $distance_query .= ")))";

      $distance_query = sprintf($distance_query, $latitude_1, $latitude_2, $longitude_1, $longitude_2, $latitude_1, $latitude_2);

      return $distance_query;
    }

    /**
     * Get an escaped IN query to use in you SQL.
     * An empty array wil return 0 (which results in an empty result normally)
     * @param string $columnName name of database column
     * @param array $values array of values
     * @param bool $addQuotes add quotes to values
     * @param bool $addNot add NOT statement, to exclude the items in the array (if array is empty, then it is the same as to ignore the query so we return 1)
     * @return string in format columname IN ('1','2')
     * if empty array/value return 0
     */
    public static function getSqlIn(string $columnName, array $values, bool $addQuotes = true, bool $addNot = false): string {
      if (empty($values)) return $addNot ? " 1 " : " 0 ";
      $where = " ".$columnName;
      if($addNot) {
        $where .= " NOT";
      }
      $where .= " IN (";
      foreach ($values as $value) {
        if($addQuotes) $where .= "'";
        $where .= self::escape($value);
        if($addQuotes) $where .= "'";
        $where .= ",";
      }
      $where = substr($where, 0, strlen($where) - 1);
      $where .= ') ';
      return $where;
    }

  }
