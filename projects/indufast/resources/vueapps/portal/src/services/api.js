import axios from 'axios';
import {useSnackbarStore} from '@/stores/snackbar';

const snackbarStore = useSnackbarStore();

const createApiService = (router) => {
  const api = axios.create({
    baseURL: import.meta.env.MODE === 'development'
      ? 'https://portal-indufast.nl.indufast.localhost/nl/api'
      : '/nl/api',
    timeout: 10000,
    withCredentials: true,
  });

  api.interceptors.response.use(
    response => {
      const endpoint = response.request.responseURL.substring(response.config.baseURL.length);

      if (response.data && response.data._query_count >= 10) {
        snackbarStore.showMessage(endpoint + " - " + response.data._query_count + " queries", "alert");
      }
      else if (response.data._query_duration >= 25) {
        snackbarStore.showMessage(endpoint + " - queries: " + response.data._query_duration + " ms", "alert");
      }

      return response;
    },
    error => {
      if (error.response) {
        if (error.response.status === 401) {
          router.push({ name: '/login' });
        }
        else if (error.response.status === 403) {
          snackbarStore.showMessage("Toegang geweigerd (403)", "error");
        }
        else {
          console.error(`API error: ${error.response.status} - ${error.response.data.message}`);
        }
      } else {
        console.error('API error: ', error.message);
      }
      return Promise.reject(error);
    }
  );

  return api;
};

export default createApiService;
