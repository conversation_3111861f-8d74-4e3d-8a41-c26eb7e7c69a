<script setup>
import {ref, computed, nextTick, watch} from "vue";
import {useRoute, useRouter} from 'vue-router';
import createApiService from '@/services/api.js';
import {formatDate, isBefore, startOfDay} from "date-fns";
import {translateErrors} from "@/helpers/translateErrors.js";
import {vMaska} from "maska/vue"
import {useSnackbarStore} from '@/stores/snackbar.js';
import {materialLoadTypes, plannable, projectStatus} from "@/helpers/constants.js";
import {debounce, deepToRaw, eventHasInternalConflicts, eventHasExternalConflicts, isValidTime, isEventInPast} from "@/helpers/helpers.js";
import Editor from "@/components/TinyMCE.vue";
import GoogleDrivePicker from "@/components/project/GoogleDrivePicker.vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

const router = useRouter();
const route = useRoute();
const api = createApiService(router);
const workDates = ref([]);
const snackbarStore = useSnackbarStore();
const editProjectDialogBusy = ref(false);
const editProjectData = ref({});
const editProjectDeletedEvents = ref([]);
const selectedEventIndex = ref(null);
const errors = ref({});
const datePickers = ref([]);

const showAddBlastDatePicker = ref(false);
const newBlastDate = ref(null);

const locationSearchResults = ref([]);
const locationSearchLoading = ref(false);

watch(() => props.modelValue, async (value) => {
  if (value) {
    await initializeData();
  }
});

watch(() => props.projectData, async () => {
  if (props.modelValue) {
    await initializeData();
  }
});

watch(() => workDates.value, () => {
  // close the date picker if it's open
  if (selectedEventIndex.value !== null && datePickers.value[selectedEventIndex.value]) {
    datePickers.value[selectedEventIndex.value].closeMenu();
    selectedEventIndex.value = null;
  }
})

const initializeData = async () => {
  editProjectDeletedEvents.value = [];
  errors.value = {};
  editProjectData.value = structuredClone(deepToRaw(props.projectData))

  workDates.value = [];
  if (!editProjectData.value.events) {
    editProjectData.value.events = [];
  }

  for (const event of editProjectData.value.events) {
    event._startTime = formatDate(event.start, "HH:mm");
    event._endTime = formatDate(event.end, "HH:mm");
    event._loading = false;
    event._errors = [];
    event._dateChanged = false;
    event._filesLoading = false;
    event.google_drive_files = event.google_drive_files || [];

    if (event.type === 'work') {
      workDates.value.push(new Date(event.start));
    }

    // Load files for existing events
    if (event.id) {
      loadEventFiles(event);
    }
  }
}

const otherWorkEventDates = computed(() => {
  const otherWorkEventDates = editProjectData.value.events
      .filter((_, index) => index !== selectedEventIndex.value && editProjectData.value.events[index].type === 'work')
      .map(event => new Date(event.start));
  return otherWorkEventDates.map(date => formatDate(date, "yyyy-MM-dd"));
});

const isPastDate = (date) => {
  return isBefore(date, startOfDay(new Date()));
};

const isDisabledDate = (date) => {
  const formattedDate = formatDate(date, "yyyy-MM-dd");
  return isPastDate(date) || otherWorkEventDates.value.includes(formattedDate);
};

const blastMarkers = computed(() => {
  return editProjectData.value.events
    .filter(event => event.type === 'blast')
    .map(event => ({
      date: new Date(event.start),
      type: 'dot',
      tooltip: [{
        color: 'orange',
        text: 'Straaldag',
      }],
      color: 'orange'
    }));
});

const saveEventDate = (newDate) => {
  if (selectedEventIndex.value !== null) {
    const event = editProjectData.value.events[selectedEventIndex.value];
    const oldDate = new Date(event.start);

    // Check if the new date is the same as the old date
    if (formatDate(newDate, "yyyy-MM-dd") === formatDate(oldDate, "yyyy-MM-dd")) {
      return;
    }

    // Update the event object itself
    event._dateChanged = true;
    event.start = formatDate(newDate, "yyyy-MM-dd") + ' ' + event._startTime + ':00';
    event.end = formatDate(newDate, "yyyy-MM-dd") + ' ' + event._endTime + ':00';

    // If the event is a work event, update the workDates array
    if (event.type === 'work') {
      const oldDateIndex = workDates.value.findIndex(date =>
        formatDate(date, "yyyy-MM-dd") === formatDate(oldDate, "yyyy-MM-dd"));

      if (oldDateIndex !== -1) workDates.value.splice(oldDateIndex, 1);
      workDates.value.push(new Date(newDate));
    }

    // Force Vue to recognize the changes
    editProjectData.value = {...editProjectData.value};

    sortProjectEvents();
  }
};

const createEventObject = (date, type, editProjectData) => {
  let startTime = '07:00';
  let endTime = '18:00';

  // Use existing times from the first event if available
  if (editProjectData.value.events.length > 0) {
    startTime = editProjectData.value.events[0]._startTime;
    endTime = editProjectData.value.events[0]._endTime;
  }

  return {
    start: date + ' ' + startTime + ':00',
    _startTime: startTime,
    end: date + ' ' + endTime + ':00',
    _endTime: endTime,
    project_id: editProjectData.value.id,
    _errors: [],
    _filesLoading: false,
    employees: [],
    google_drive_files: [],
    type,
  };
};

const updateEvents = (selectedDates) => {
  let datesOnly = [];
  for (let i in selectedDates) {
    datesOnly.push(formatDate(selectedDates[i], "yyyy-MM-dd"));
  }

  // Add missing 'work' event dates.
  for (let i in datesOnly) {
    // Only add a new 'work' event if no work event exists for this date.
    if (!editProjectData.value.events.some(event => formatDate(event.start, "yyyy-MM-dd") === datesOnly[i] && event.type === 'work')) {
      const newWorkEvent = createEventObject(datesOnly[i], 'work', editProjectData);
      editProjectData.value.events.push(newWorkEvent);
    }
  }

  for (let i = editProjectData.value.events.length - 1; i >= 0; i--) {
    const event = editProjectData.value.events[i];
    // Only consider 'work' events for deletion based on calendar selection.
    if (event.type === 'work' && !datesOnly.includes(formatDate(event.start, "yyyy-MM-dd")) && !isEventInPast(event)) {
      if (event.id) {
        editProjectDeletedEvents.value.push(event.id);
      }
      editProjectData.value.events.splice(i, 1);
    }
  }

  sortProjectEvents();
};

const sortProjectEvents = () => {
  // Sort events by date and status (work events first, then blast events).
  editProjectData.value.events.sort((a, b) => {
    const dateA = new Date(a.start);
    const dateB = new Date(b.start);

    // Compare dates first
    if (dateA.getTime() !== dateB.getTime()) {
      return dateA.getTime() - dateB.getTime();
    }

    // If dates are the same, compare by type
    const typeOrder = { 'work': 0, 'blast': 1 }; // Assign a numerical order
    return typeOrder[a.type] - typeOrder[b.type];
  });
};

const addBlastEvent = (date) => {
  if (!date) return;

  const formattedDate = formatDate(date, "yyyy-MM-dd");

  const newBlastEvent = createEventObject(formattedDate, 'blast', editProjectData);
  editProjectData.value.events.push(newBlastEvent);

  sortProjectEvents();

  showAddBlastDatePicker.value = false;
  newBlastDate.value = null;
};


const deleteEvent = (event) => {
  const eventIndex = editProjectData.value.events.findIndex(e => e === event);
  if (eventIndex !== -1) {
    editProjectData.value.events.splice(eventIndex, 1);
  }

  // Only remove from workDates if it's a 'work' event
  if (event.type === 'work') {
    const dateIndex = workDates.value.findIndex(date => formatDate(date, "yyyy-MM-dd") === formatDate(new Date(event.start), "yyyy-MM-dd"));
    if (dateIndex !== -1) {
      workDates.value.splice(dateIndex, 1);
    }
  }

  if (event.id) {
    editProjectDeletedEvents.value.push(event.id);
  }
}

const copyEventTimes = (event) => {
  for (const e of editProjectData.value.events) {
    if (e.type !== 'work' || isPastDate(e.start)) continue;

    e._startTime = event._startTime;
    e._endTime = event._endTime;
    updateEventTimes(e);
  }
}

const updateEventTimes = (event) => {
  event.start = event.start.split(' ')[0] + (isValidTime(event._startTime) ? ' ' + event._startTime + ':00' : '');
  event.end = event.start.split(' ')[0] + (isValidTime(event._endTime) ? ' ' + event._endTime + ':00' : '');
}

const submitEditProject = async () => {
  editProjectDialogBusy.value = true;

  let deletedEventCount = editProjectDeletedEvents.value.length;
  let eventCount = editProjectData.value.events.length + deletedEventCount;
  let hasErrors = false;

  const uri = editProjectData.value.id
    ? `projectUpdate?id=${editProjectData.value.id}`
    : "projectCreate";

  try {
    // We update the project first, then the events.
    const response = await api.post(uri, JSON.stringify(editProjectData.value));
    editProjectData.value.id = response.data.data.id;

    if (eventCount === 0) {
      closeDialog();
      snackbarStore.showMessage("Project succesvol opgeslagen!", "success");
      emit("saved");
      return;
    }

    const deletePromises = editProjectDeletedEvents.value.map((eventId, index) =>
      api
        .post(`eventDelete?id=${eventId}`)
        .then(() => {
          editProjectDeletedEvents.value.splice(index, 1);
          snackbarStore.showMessage("Project succesvol opgeslagen!", "success");
        })
        .catch((error) => {
          editProjectDialogBusy.value = false;
          errors.value = error.response?.data?.data;
          snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
        })
    );

    // Filter out past events - they should not be sent to the backend
    const eventsToUpdate = editProjectData.value.events.filter(event => !isEventInPast(event));

    const updateCreatePromises = eventsToUpdate.map((event) => {
      event._loading = true;
      event.employee_ids = event.employees.map((e) => e.employee.id);
      event.project_id = editProjectData.value.id;

      const uri = event.id ? `eventUpdate?id=${event.id}` : "eventCreate";

      return api
        .post(uri, JSON.stringify(event))
        .then(() => {
          event._loading = false;
          snackbarStore.showMessage("Project succesvol opgeslagen!", "success");
        })
        .catch((error) => {
          event._loading = false;
          event._errors = error.response?.data?.data;
          hasErrors = true;
        });
    });

    // The deletion and updates are independent, so we can run them in parallel.
    await Promise.all([...deletePromises, ...updateCreatePromises]);

    // All events were deleted successfully.
    editProjectDeletedEvents.value = [];

    if (!hasErrors) {
      closeDialog();
      snackbarStore.showMessage("Project succesvol opgeslagen!", "success");
      emit("saved");
    }
    else {
      snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
      editProjectDialogBusy.value = false;
    }

    // Update the employee availability
    // Wait 5 seconds before updating the employee availability
    await new Promise(resolve => setTimeout(resolve, 5000));
    await api.put("employeeAvailabilityUpdate");
  } catch (error) {
    editProjectDialogBusy.value = false;
    errors.value = error.response?.data?.data;
    snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");

    await nextTick()
    const el = document.querySelector(".v-input--error:first-of-type, .v-field--error:first-of-type");
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
};

const closeDialog = () => {
  emit('update:modelValue', false);
  editProjectDialogBusy.value = false;
}

const openPlanningPage = (event) => {
  closeDialog();

  const date = new Date(event.start);
  router.push({
    name: '/planning', params: {
      date: formatDate(date, "yyyy-MM-dd"),
    }
  });
}

const isCurrentSelectedDate = (event) => {
  const date = new Date(event.start);
  return (formatDate(date, "yyyy-MM-dd") === route.params.date)
}

const searchLocations = debounce(async (query) => {
  if (props.readonly) return;

  if (!query || query.length < 3) {
    locationSearchResults.value = [];
    return;
  }

  locationSearchLoading.value = true;
  try {
    const response = await api.get(`searchLocations?query=${encodeURIComponent(query.trim())}`);
    locationSearchResults.value = response.data.data;
  } catch (error) {
    console.error('Error searching locations:', error);
    locationSearchResults.value = [];
  } finally {
    locationSearchLoading.value = false;
  }
}, 300);

const loadEventFiles = async (event) => {
  if (!event.id) return;

  event._filesLoading = true;
  try {
    const response = await api.get(`eventGetGoogleFiles?id=${event.id}`);
    event.google_drive_files = response.data.data || [];
  } catch (error) {
    console.error("Error fetching event files:", error);
    snackbarStore.showMessage("Fout bij ophalen van bestanden!", "error");
  } finally {
    event._filesLoading = false;
  }
};

const handleGoogleDrivePicked = (files, event) => {
  files.forEach(file => {
    const url = file.url || file.webViewLink || file.alternateLink;
    if (!event.google_drive_files.some(f => f.url === url)) {
      event.google_drive_files.push({
        name: file.name,
        url: url,
        icon_url: file.iconUrl || file.iconLink
      });
    }
  });
};

const copyFileToAllEvents = (sourceFile, sourceEvent) => {
  if (!sourceFile) return;

  let copiedCount = 0;
  editProjectData.value.events.forEach(targetEvent => {
    if (targetEvent === sourceEvent || isPastDate(targetEvent.start)) return;

    const fileExists = targetEvent.google_drive_files.some(targetFile =>
      targetFile.url === sourceFile.url
    );

    if (!fileExists) {
      targetEvent.google_drive_files.push({
        name: sourceFile.name,
        url: sourceFile.url,
        icon_url: sourceFile.icon_url
      });
      copiedCount++;
    }
  });

  if (copiedCount > 0) {
    snackbarStore.showMessage(`Bestand gekopieerd naar ${copiedCount} andere datum${copiedCount > 1 ? 's' : ''}!`, "success");
  } else {
    snackbarStore.showMessage("Bestand bestaat al op alle andere datums!", "info");
  }
};

const getEventBadgeColor = (event) => {
  if (event.type === 'blast') {
    return 'orange';
  }
  if (eventHasInternalConflicts(event)) {
    return 'indufastPurple';
  }
  if (eventHasExternalConflicts(event)) {
    return 'indufastRed';
  }
  return 'indufastBlue';
}
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    min-width="1200"
    max-width="1600"
    max-height="90%"
    scrollable
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <v-toolbar
      :color="projectStatus.find(status => status.value === editProjectData.status)?.color ?? 'primary'"
    >
      <v-toolbar-title>
        <v-icon
          v-if="editProjectData.plannable === plannable.NOT_PLANNABLE_EXTERNAL"
          class="mr-1"
          size="small"
          title="Dit project heeft medewerkers die niet meer beschikbaar zijn op dit moment"
        >
          mdi-alert-circle-outline
        </v-icon>
        <v-icon
          v-else-if="editProjectData.plannable === plannable.NOT_PLANNABLE_INTERNAL"
          class="mr-1"
          size="small"
          title="Dit project heeft medewerkers die dubbel zijn ingepland"
        >
          mdi-alert-circle-outline
        </v-icon>
        {{ editProjectData.id ? 'Project "' + editProjectData.name + '" bewerken' : 'Nieuw project' }}
      </v-toolbar-title>
      <v-toolbar-items>
        <v-btn
          icon
          @click="closeDialog"
        >
          <v-icon>
            mdi-close
          </v-icon>
        </v-btn>
      </v-toolbar-items>
    </v-toolbar>
    <v-card>
      <v-card-text class="pa-2">
        <v-row no-gutters>
          <v-col>
            <v-row no-gutters>
              <v-col>
                <v-card>
                  <v-card-title>Project</v-card-title>
                  <v-card-text>
                    <v-text-field
                      v-model="editProjectData.name"
                      label="Naam"
                      :error-messages="translateErrors(errors.name, 'Naam')"
                      rows="4"
                      class="required"
                      :readonly="readonly"
                    />
                    <Editor
                      v-model="editProjectData.remark"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.remark, 'Omschrijving')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col>
                <v-card>
                  <v-card-text>
                    <v-select
                      v-model="editProjectData.status"
                      label="Status"
                      :items="projectStatus"
                      :error-messages="translateErrors(errors.status, 'Status')"
                      :readonly="readonly"
                      class="required mb-5"
                    />
                    <v-select
                      v-model="editProjectData.material_load"
                      label="Materiaal laden"
                      :clearable="!readonly"
                      :items="materialLoadTypes"
                      :error-messages="translateErrors(errors.material_load, 'Materiaal laden')"
                      :readonly="readonly"
                      class="mb-5"
                    />
                    <v-number-input
                      v-model="editProjectData.fte"
                      label="FTE"
                      control-variant="stacked"
                      :min="1"
                      :max="100"
                      :clearable="!readonly"
                      inset
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.fte, 'FTE', false)"
                      class="mb-5"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-text>
                    <v-text-field
                      v-model="editProjectData.project_number"
                      label="Projectnummer"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.project_number, 'Nummer')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editProjectData.project_number_exact"
                      label="Projectnummer exact"
                      class="required mb-5"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.project_number_exact, 'Nummer exact')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col>
                <v-card-title>
                  Klant
                  <v-btn
                    :disabled="!editProjectData.address"
                    :href="'https://maps.google.com/?q=' + encodeURIComponent(editProjectData.address)"
                    target="_blank"
                    icon="mdi-map-marker"
                    variant="plain"
                    color="primary"
                  />
                </v-card-title>

                <v-card-text>
                  <v-text-field
                    v-model="editProjectData.customer_name"
                    label="Naam"
                    class="required mb-5"
                    :readonly="readonly"
                    :error-messages="translateErrors(errors.customer_name, 'Naam')"
                  />
                  <v-combobox
                    v-model="editProjectData.address"
                    :items="locationSearchResults"
                    :loading="locationSearchLoading"
                    :title="editProjectData.address"
                    item-title="description"
                    item-value="description"
                    label="Adres"
                    class="required mb-5"
                    :error-messages="translateErrors(errors.address, 'Adres')"
                    hide-no-data
                    hide-selected
                    :return-object="false"
                    :clearable="!readonly"
                    :readonly="readonly"
                    :custom-filter="() => {}"
                    @update:search="searchLocations"
                  >
                    <template #item="{ props, item }">
                      <v-list-item v-bind="props">
                        <v-list-item-subtitle>{{ item.raw.structured_formatting.secondary_text }}</v-list-item-subtitle>
                      </v-list-item>
                    </template>
                  </v-combobox>
                </v-card-text>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-title>Contactpersoon</v-card-title>
                  <v-card-text>
                    <v-text-field
                      v-model="editProjectData.contact_name"
                      label="Naam"
                      class="mb-5"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.contact_name, 'Naam contactpersoon')"
                    />
                    <v-text-field
                      v-model="editProjectData.contact_email"
                      label="E-mailadres"
                      class="mb-5"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.contact_email, 'E-mailadres')"
                    />
                    <v-text-field
                      v-model="editProjectData.contact_number"
                      label="Telefoonnummer"
                      class="mb-5"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.contact_number, 'Telefoonnummer')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
          <v-col>
            <v-card>
              <v-card-title>{{ 'Datums (' + editProjectData.events.length + ')' }}</v-card-title>
              <v-card-text>
                <v-row>
                  <v-col>
                    <vue-date-picker
                      v-model="workDates"
                      :multi-dates="{dragSelect: false}"
                      :markers="blastMarkers"
                      :disabled-dates="isPastDate"
                      locale="nl-NL"
                      week-num-name="#"
                      inline
                      week-numbers="iso"
                      :enable-time-picker="false"
                      :year-range="[2025, new Date().getFullYear() + 1]"
                      :month-change-on-scroll="false"
                      auto-apply
                      multi-calendars
                      hide-offset-dates
                      six-weeks
                      :readonly="readonly"
                      @update:model-value="updateEvents"
                    />
                  </v-col>
                  <v-col>
                    <vue-date-picker
                      v-if="!readonly"
                      v-model="newBlastDate"
                      locale="nl-NL"
                      :enable-time-picker="false"
                      auto-apply
                      teleport
                      class="date-picker-blast"
                      @update:model-value="addBlastEvent"
                      @closed="newBlastDate = null"
                    >
                      <template #trigger>
                        <v-btn
                          class="mb-4"
                          color="orange-darken-2"
                        >
                          Straaldag toevoegen
                        </v-btn>
                      </template>
                    </vue-date-picker>

                    <v-card
                      v-for="(event, index) in editProjectData.events"
                      :key="index"
                      flat
                      :loading="event._loading"
                      class="mb-2"
                    >
                      <v-card-title
                        class="d-flex align-center ga-2 overflow-visible"
                        :class="{
                          'text-primary': selectedEventIndex === index,
                          'text-indufastPurple': (eventHasInternalConflicts(event) && !event._dateChanged),
                          'text-indufastRed': (eventHasExternalConflicts(event) && !event._dateChanged),
                          'text-blue': event._dateChanged,
                          'text-orange-darken-2': event.type === 'blast',
                          'text-grey': isEventInPast(event)
                        }"
                      >
                        <v-icon
                          v-if="isEventInPast(event)"
                          size="small"
                          title="Deze datum ligt in het verleden en kan niet worden bewerkt"
                          color="grey"
                        >
                          mdi-lock
                        </v-icon>
                        <v-icon
                          v-if="event.type === 'blast'"
                          size="small"
                          title="Stralen"
                        >
                          mdi-handshake
                        </v-icon>
                        <v-icon
                          v-if="eventHasInternalConflicts(event) && !event._dateChanged"
                          size="small"
                          title="Deze datum heeft dubbel ingeplande medewerkers"
                        >
                          mdi-alert-circle-outline
                        </v-icon>
                        <v-icon
                          v-if="eventHasExternalConflicts(event) && !event._dateChanged"
                          size="small"
                          title="Deze datum heeft medewerkers die niet beschikbaar zijn op dit moment"
                        >
                          mdi-alert-circle-outline
                        </v-icon>

                        {{ $filters.ucFirst($filters.formatDate(event.start)) }}{{ event.type === 'blast' ? ": Stralen" : "" }}

                        <vue-date-picker
                          v-if="!readonly && !isEventInPast(event)"
                          ref="datePickers"
                          class="date-picker"
                          locale="nl-NL"
                          :config="{ allowPreventDefault: true }"
                          :enable-time-picker="false"
                          :disabled-dates="isDisabledDate"
                          teleport
                          auto-apply
                          @update:model-value="saveEventDate"
                          @open="selectedEventIndex = index"
                          @closed="selectedEventIndex = null"
                        >
                          <template #trigger>
                            <v-icon
                              class="cursor-pointer"
                              size="small"
                              color="primary"
                              title="Datum wijzigen"
                            >
                              mdi-calendar-edit
                            </v-icon>
                          </template>
                        </vue-date-picker>
                        <v-menu
                          v-if="!readonly && !isEventInPast(event)"
                          location="bottom"
                          :close-on-content-click="false"
                        >
                          <template #activator="{ props }">
                            <v-icon
                              class="cursor-pointer"
                              v-bind="props"
                              size="small"
                              color="indufastGreen"
                              :title="event.remark ? 'Opmerking bewerken' : 'Opmerking toevoegen'"
                            >
                              {{ event.remark ? 'mdi-note-edit' : 'mdi-note-plus' }}
                            </v-icon>
                          </template>

                          <v-list>
                            <v-list-item>
                              <v-textarea
                                v-model="event.remark"
                                label="Opmerking"
                                hide-details
                                clearable
                              />
                            </v-list-item>
                          </v-list>
                        </v-menu>
                        <google-drive-picker
                          v-if="!readonly && !isEventInPast(event) && !event._filesLoading"
                          :disabled="event._filesLoading"
                          @picked="(files) => handleGoogleDrivePicked(files, event)"
                        />
                        <v-progress-circular
                          v-if="event._filesLoading"
                          color="primary"
                          indeterminate
                          size="16"
                          class="mr-2"
                        />
                      </v-card-title>
                      <v-card-text class="pb-0">
                        <v-row dense>
                          <v-col>
                            <v-text-field
                              v-model="event._startTime"
                              v-maska="'##:##'"
                              placeholder="07:00"
                              label="Start"
                              density="compact"
                              :readonly="readonly || isEventInPast(event)"
                              :error-messages="translateErrors(event._errors.start, 'Starttijd')"
                              @change="updateEventTimes(event)"
                            />
                          </v-col>
                          <v-col>
                            <v-text-field
                              v-model="event._endTime"
                              v-maska="'##:##'"
                              label="Einde"
                              placeholder="18:00"
                              density="compact"
                              :readonly="readonly || isEventInPast(event)"
                              :error-messages="translateErrors(event._errors.end, 'Eindtijd')"
                              @change="updateEventTimes(event)"
                            />
                          </v-col>
                          <v-col>
                            <v-number-input
                              v-if="event.type === 'work'"
                              v-model="event.fte"
                              title="Laat leeg om de FTE van het project te gebruiken"
                              :label="!event.fte && editProjectData.fte ? editProjectData.fte + ' FTE' : 'FTE'"
                              :placeholder="!event.fte && editProjectData.fte ? editProjectData.fte + ' FTE' : 'FTE'"
                              control-variant="stacked"
                              :min="1"
                              :max="100"
                              :clearable="!readonly && !isEventInPast(event)"
                              :readonly="readonly || isEventInPast(event)"
                              inset
                              density="compact"
                            />
                            <v-checkbox
                              v-else-if="event.type === 'blast'"
                              v-model="event.confirmed"
                              label="Bevestigd"
                              density="compact"
                              :readonly="readonly || isEventInPast(event)"
                              hide-details
                              color="primary"
                              class="ml-3"
                            />
                          </v-col>
                          <v-col class="d-flex align-baseline">
                            <v-btn
                              v-if="!readonly && !isEventInPast(event)"
                              icon
                              color="indufastRed"
                              size="small"
                              class="mr-2"
                              title="Verwijder datum"
                              @click="deleteEvent(event)"
                            >
                              <v-icon>
                                mdi-delete
                              </v-icon>
                            </v-btn>
                            <v-btn
                              v-if="!readonly && !isEventInPast(event) && event.type === 'work'"
                              icon
                              color="indufastBlue"
                              size="small"
                              class="mr-2"
                              title="Kopieer tijden naar alle datums"
                              @click="copyEventTimes(event)"
                            >
                              <v-icon>
                                mdi-update
                              </v-icon>
                            </v-btn>

                            <v-badge
                              :content="event.employees.length"
                              :color="getEventBadgeColor(event)"
                              :model-value="!!event.employees.length"
                              :title="event.employees.length + ' medewerkers ingepland'"
                              overlap
                              bordered
                            >
                              <v-btn
                                v-if="event.id"
                                icon
                                color="indufastGreen"
                                size="small"
                                title="Open planning"
                                @click="(isCurrentSelectedDate(event) && route.name === '\planning') ? closeDialog() : openPlanningPage(event)"
                              >
                                <v-icon>
                                  mdi-clipboard-account
                                </v-icon>
                              </v-btn>
                            </v-badge>
                          </v-col>
                        </v-row>
                        <v-row
                          v-if="event.remark || event.google_drive_files.length"
                          dense
                          class="mb-2 mt-0"
                        >
                          <v-col cols="10">
                            <v-list
                              density="compact"
                              class="pt-0"
                            >
                              <v-list-item
                                v-if="event.remark"
                                prepend-icon="mdi-note"
                                :title="event.remark"
                                slim
                              />
                              <v-list-item
                                v-for="(file, fileIdx) in event.google_drive_files"
                                :key="fileIdx"
                                slim
                              >
                                <template #prepend>
                                  <v-icon>
                                    <img
                                      :src="file.icon_url"
                                      alt="icon"
                                    >
                                  </v-icon>
                                </template>
                                <v-list-item-title>
                                  <a
                                    :href="file.url"
                                    target="_blank"
                                    rel="noopener"
                                    class="text-decoration-none"
                                  >{{ file.name }}</a>
                                </v-list-item-title>
                                <template #append>
                                  <v-icon
                                    v-if="!readonly && !isEventInPast(event)"
                                    icon="mdi-delete"
                                    :disabled="event._filesLoading"
                                    color="indufastRed"
                                    title="Verwijder bestand"
                                    class="ml-2"
                                    @click="event.google_drive_files.splice(fileIdx, 1)"
                                  />
                                  <v-icon
                                    v-if="!readonly && editProjectData.events.length > 1"
                                    icon="mdi-content-copy"
                                    color="indufastBlue"
                                    title="Kopieer bestand naar alle andere datums"
                                    class="ml-2"
                                    @click="copyFileToAllEvents(file, event)"
                                  />
                                </template>
                              </v-list-item>
                            </v-list>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions v-if="!readonly">
        <v-checkbox
          v-model="editProjectData.void"
          hide-details
          density="compact"
          class="mr-2"
          label="Gearchiveerd"
        />
        <v-spacer />
        <v-btn
          variant="elevated"
          color="primary"
          prepend-icon="mdi-content-save"
          :loading="editProjectDialogBusy"
          @click="submitEditProject"
        >
          Opslaan
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.date-picker {
  width: 1.25em;
}
.date-picker-blast {
  width: 15em;
}
</style>
