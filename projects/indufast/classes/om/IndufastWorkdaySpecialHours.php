<?php

  use classes\HydrateTrait;
  use classes\LogTrait;
  use classes\TimesTrait;

  AppModel::loadModelClass('IndufastWorkdaySpecialHoursModel');

  class IndufastWorkdaySpecialHours extends IndufastWorkdaySpecialHoursModel {

    use ModelFillTrait;
    use ModelTimeTrait;
    use ValidationTrait;
    use PropertyCastTrait;
    use TimesTrait;
    use LogTrait;
    use HydrateTrait;

    const string TYPE_LEAVE = 'leave';
    const string TYPE_SPECIAL_LEAVE = 'special-leave';
    const string TYPE_UNEXCUSED_LEAVE = 'unexcused-leave';
    const string TYPE_SICK = 'sick';

    public IndufastWorkday $workday;
    public int $parent_id;

    protected array $fillable = [
      'workday_id' => 'required|integer|exists:indufast_workday,id',
      'type'       => 'required|in:leave,special-leave,unexcused-leave,sick',
    ];

    const array CAST_PROPERTIES = [
      'id'         => 'int',
      'workday_id' => 'int',
      'from_db'    => 'hidden',
    ];

    public function getFillable(): array {
      $this->fillable['duration'] = [
        'required',
        'callback' => fn($v) => IndufastWorkdaySpecialHours::validTime($v),
      ];

      return $this->fillable;
    }

    protected function workday(): IndufastWorkday {
      return $this->workday ?? $this->workday = IndufastWorkday::find_by_id($this->workday_id);
    }

    public function parent_name() {
      return IndufastEmployee::class;
    }

    public function parent_id() {
      return $this->parent_id ?? $this->parent_id = $this->workday()->employee_id;
    }
  }