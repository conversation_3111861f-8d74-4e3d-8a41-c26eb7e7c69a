<?php

  use classes\LogTrait;

  AppModel::loadModelClass('IndufastWorkdaySummaryModel');

class IndufastWorkdaySummary extends IndufastWorkdaySummaryModel {

  use ModelFillTrait;
  use ModelTimeTrait;
  use ValidationTrait;
  use PropertyCastTrait;

  const array CAST_PROPERTIES = [
    'id'              => 'int',
    'year'            => 'int',
    'month'           => 'int',
    'employee_id'     => 'int',
    'from_db'         => 'hidden',
    'locked'          => 'boolean',
  ];

  public IndufastEmployee $employee;
  public static function setMonthlyBalance(int $year, int $month, int $employeeId, string $monthlyBalance): void {
    $summary = self::find_by(['year' => $year, 'month' => $month, 'employee_id' => $employeeId]);
    if (!$summary) {
      $summary = new self();
      $summary->year = $year;
      $summary->month = $month;
      $summary->employee_id = $employeeId;
    }
    $summary->monthly_balance = $monthlyBalance;
    $summary->save();
  }
}