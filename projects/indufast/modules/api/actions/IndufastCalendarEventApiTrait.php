<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventApiTrait {

    public function executeEventCreate(): void {
      $event = new IndufastCalendarEvent();
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();

        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    public function executeEventUpdate(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }
      $event->fill($this->data)->validateFillable();

      try {
        if (!is_null($this->data['employee_ids'] ?? null)) {
          $event->updateEmployees();
        }
        $event->save();
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '', $e);
      }
    }

    public function executeEventDelete(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }

      try {
        $event->destroy();
        ApiResponse::sendResponseOK('Event deleted', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while deleting.', [], $e);
      }
    }

    public function executeEventGetGoogleFiles(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Calendar event not found');
      }

      try {
        $files = $event->getGoogleFiles();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $files);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.', [], $e);
      }
    }

  }