<?php

  namespace domain\workday\service;

  use classes\TimesTrait;
  use Config;
  use GsdMailer;
  use IndufastWorkday;

  class WorkdayMailer {
    use TimesTrait;

    public function send(): void {
      try {
        // Get all active employees
        $employees = \IndufastEmployee::find_all();
        $activeEmployees = \IndufastEmployee::filterActiveEmployees($employees);

        // Calculate date range for last week (Monday to Sunday)
        $endDate = date('Y-m-d', strtotime('last Sunday'));
        $startDate = date('Y-m-d', strtotime('last Monday', strtotime($endDate)));

        foreach ($activeEmployees as $employee) {
          $this->sendEmployeeWeeklyReport($employee, $startDate, $endDate);
        }
      } catch (\Exception $e) {
        dump($e->getMessage());
      }
    }

    /**
     * @throws \GsdException
     * @throws \Exception
     */
    private function sendEmployeeWeeklyReport(\IndufastEmployee $employee, string $startDate, string $endDate): void {
      // Get workdays for the week.
      if (!$workdays = \AppModel::mapObjectIds(IndufastWorkday::find_all_by([
          'employee_id' => $employee->id,
          'status' => IndufastWorkday::STATUS_PROCESSED,
        ],
        "AND date BETWEEN '$startDate' AND '$endDate'"
      ), 'date')) {
        return;
      }

      // Calculate workdays and ensure we have data for each day.
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      // Generate email content.
      $emailContent = $this->generateEmailContent($employee, $startDate, $endDate, $workdays);

      // Send email using GsdMailer.
      $weekNumber = date('W', strtotime($startDate));
      $subject = "Indufast - Overzicht uren week $weekNumber";

      $mailTo = Config::get('EMAIL_TEST_MODE', true) ? Config::get('EMAIL_FROM') : $employee->email;
      GsdMailer::build($mailTo, $subject, $emailContent)->send();
    }

    private function generateEmailContent(\IndufastEmployee $employee, string $startDate, string $endDate, array $workdaysByDate): string {
      $weekNumber = date('W', strtotime($startDate));
      $formattedStartDate = date('d-m-Y', strtotime($startDate));
      $formattedEndDate = date('d-m-Y', strtotime($endDate));

      // Generate table rows
      $tableRows = $this->generateTableRows($workdaysByDate, $startDate, $endDate);

      return <<<HTML
          <!DOCTYPE html>
          <html lang="nl">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Overzicht uren week {$weekNumber}</title>
          </head>
          <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
              <div style="max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                  <div style="text-align: center; margin-bottom: 30px;">
                      <img src="{$this->getLogoBase64()}" alt="Indufast Logo" style="max-height: 60px; height: auto;">
                  </div>

                  <h1 style="color: #353535; margin-bottom: 20px; border-bottom: 3px solid #dd0015; padding-bottom: 10px;">
                      Overzicht uren week {$weekNumber}
                  </h1>
                  
                  <div style="margin-bottom: 20px;">
                      <p><strong>Medewerker:</strong> {$employee->name}</p>
                      <p><strong>Periode:</strong> {$formattedStartDate} - {$formattedEndDate}</p>
                  </div>
          
                  <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                      <thead>
                          <tr style="background-color: #dd0015; color: white;">
                              <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Dag</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Werkuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Reisuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Verlof</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Bijzonder verlof</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Ziek</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Ongeoorloofd afwezig</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Overuren</th>
                          </tr>
                      </thead>
                      <tbody>
                          {$tableRows}
                      </tbody>
                  </table>
              </div>
          </body>
          </html>
          HTML;
    }

    private function generateTableRows(array $workdaysByDate, string $startDate, string $endDate): string {
      $rows = '';
      $current = strtotime($startDate);
      $end = strtotime($endDate);

      // Arrays to collect times for totals
      $totalWorkHours = [];
      $totalTravelHours = [];
      $totalLeaveHours = [];
      $totalSpecialLeaveHours = [];
      $totalSickHours = [];
      $totalUnexcusedLeaveHours = [];
      $totalOvertimeHours = [];

      while ($current <= $end) {
        $date = date('Y-m-d', $current);
        $dayName = $this->getDutchDayName(date('w', $current));
        $formattedDate = date('d-m', $current);

        /** @var IndufastWorkday $workday */
        $workday = $workdaysByDate[$date] ?? null;
        if ($workday) {
          $workHours = $workday->workdayDurationNetClipped !== '00:00:00' ? $workday->workdayDurationNetClipped : '';
          $travelHours = $workday->travelDurationNet !== '00:00:00' ? $workday->travelDurationNet : '';
          $leaveHours = $workday->specialHoursLeave !== '00:00:00' ? $workday->specialHoursLeave : '';
          $specialLeaveHours = $workday->specialHoursSpecialLeave !== '00:00:00' ? $workday->specialHoursSpecialLeave : '';
          $sickHours = $workday->specialHoursSick !== '00:00:00' ? $workday->specialHoursSick : '';
          $unexcusedLeaveHours = $workday->specialHoursUnexcusedLeave !== '00:00:00' ? $workday->specialHoursUnexcusedLeave : '';

          $overtimeHours = '';
          if ($workday->overtimeBelow || $workday->overtimeAbove) {
            $overtimeParts = array_filter([$workday->overtimeBelow, $workday->overtimeAbove]);
            $overtimeHours = $overtimeParts ? $this->addTimes($overtimeParts) : '';
          }

          if ($workHours) $totalWorkHours[] = $workHours;
          if ($travelHours) $totalTravelHours[] = $travelHours;
          if ($leaveHours) $totalLeaveHours[] = $leaveHours;
          if ($specialLeaveHours) $totalSpecialLeaveHours[] = $specialLeaveHours;
          if ($sickHours) $totalSickHours[] = $sickHours;
          if ($unexcusedLeaveHours) $totalUnexcusedLeaveHours[] = $unexcusedLeaveHours;
          if ($overtimeHours) $totalOvertimeHours[] = $overtimeHours;

          $rows .= sprintf(
            '<tr style="border-bottom: 1px solid #eee;">
                      <td style="padding: 10px; border: 1px solid #ddd;">%s %s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                  </tr>',
            $dayName,
            $formattedDate,
            $workHours,
            $travelHours,
            $leaveHours,
            $specialLeaveHours,
            $sickHours,
            $unexcusedLeaveHours,
            $overtimeHours
          );
        }
        $current = strtotime('+1 day', $current);
      }

      // Add totals row
      $totalWorkTime = $totalWorkHours ? $this->addTimes($totalWorkHours) : '';
      $totalTravelTime = $totalTravelHours ? $this->addTimes($totalTravelHours) : '';
      $totalLeaveTime = $totalLeaveHours ? $this->addTimes($totalLeaveHours) : '';
      $totalSpecialLeaveTime = $totalSpecialLeaveHours ? $this->addTimes($totalSpecialLeaveHours) : '';
      $totalSickTime = $totalSickHours ? $this->addTimes($totalSickHours) : '';
      $totalUnexcusedLeaveTime = $totalUnexcusedLeaveHours ? $this->addTimes($totalUnexcusedLeaveHours) : '';
      $totalOvertimeTime = $totalOvertimeHours ? $this->addTimes($totalOvertimeHours) : '';

      $rows .= sprintf(
        '<tr style="border-top: 3px solid #dd0015; background-color: #f8f9fa; font-weight: bold;">
                  <td style="padding: 12px; border: 1px solid #ddd; color: #353535;">Totaal</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
              </tr>',
        $totalWorkTime,
        $totalTravelTime,
        $totalLeaveTime,
        $totalSpecialLeaveTime,
        $totalSickTime,
        $totalUnexcusedLeaveTime,
        $totalOvertimeTime,
      );

      return $rows;
    }

    private function getDutchDayName(int $dayOfWeek): string {
      $days = ['Zondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'];
      return $days[$dayOfWeek];
    }

    private function getLogoBase64(): string {
      $logoPath = __DIR__ . '/../../../templates/portal/images/logo.png';

      if (!file_exists($logoPath)) {
        return '';
      }

      $imageData = file_get_contents($logoPath);
      $base64 = base64_encode($imageData);
      $mimeType = 'image/png';

      return "data:$mimeType;base64,$base64";
    }

  }