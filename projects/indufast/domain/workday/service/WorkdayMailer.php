<?php

  namespace domain\workday\service;

  use classes\TimesTrait;
  use IndufastWorkday;

  class WorkdayMailer {
    use TimesTrait;

    public function send(): void {
      try {
        // Get all active employees
        $employees = \IndufastEmployee::find_all();
        $activeEmployees = \IndufastEmployee::filterActiveEmployees($employees);

        // Calculate date range for last week (Monday to Sunday)
        $endDate = date('Y-m-d', strtotime('last Sunday'));
        $startDate = date('Y-m-d', strtotime('last Monday', strtotime($endDate)));

        foreach ($activeEmployees as $employee) {
          $this->sendEmployeeWeeklyReport($employee, $startDate, $endDate);
        }
      } catch (\Exception $e) {
        dump($e->getMessage());
      }
    }

    private function sendEmployeeWeeklyReport(\IndufastEmployee $employee, string $startDate, string $endDate): void {
      // Get workdays for the week
      $workdays = \IndufastWorkday::find_all_by([
          'employee_id' => $employee->id,
          'status' => \IndufastWorkday::STATUS_PROCESSED,
        ],
        "AND date BETWEEN '$startDate' AND '$endDate'"
      );
      if (!$workdays) return;

      // Calculate workdays and ensure we have data for each day
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      // Create workdays array indexed by date for easy lookup
      $workdaysByDate = [];
      foreach ($workdays as $workday) {
        $workdaysByDate[$workday->date] = $workday;
      }

      // Generate email content
      $emailContent = $this->generateEmailContent($employee, $startDate, $endDate, $workdaysByDate);

      // Send email using GsdMailer
      $weekNumber = date('W', strtotime($startDate));
      $subject = "Weekoverzicht werkuren - Week $weekNumber";

      \GsdMailer::build($employee->email, $subject, $emailContent)->send();
    }

    private function generateEmailContent(\IndufastEmployee $employee, string $startDate, string $endDate, array $workdaysByDate): string {
      $weekNumber = date('W', strtotime($startDate));
      $formattedStartDate = date('d-m-Y', strtotime($startDate));
      $formattedEndDate = date('d-m-Y', strtotime($endDate));

      // Generate table rows
      $tableRows = $this->generateTableRows($workdaysByDate, $startDate, $endDate);

      return <<<HTML
          <!DOCTYPE html>
          <html lang="nl">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Weekoverzicht Week {$weekNumber}</title>
          </head>
          <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
              <div style="max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                  <h1 style="color: #353535; margin-bottom: 20px; border-bottom: 3px solid #dd0015; padding-bottom: 10px;">
                      Weekoverzicht Week {$weekNumber}
                  </h1>
                  
                  <div style="margin-bottom: 20px;">
                      <p><strong>Medewerker:</strong> {$employee->name}</p>
                      <p><strong>Periode:</strong> {$formattedStartDate} - {$formattedEndDate}</p>
                  </div>
          
                  <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                      <thead>
                          <tr style="background-color: #dd0015; color: white;">
                              <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Dag</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Werkuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Overuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Reisuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Verlof</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Ziek</th>
                          </tr>
                      </thead>
                      <tbody>
                          {$tableRows}
                      </tbody>
                  </table>
              </div>
          </body>
          </html>
          HTML;
    }

    private function generateTableRows(array $workdaysByDate, string $startDate, string $endDate): string {
      $rows = '';
      $current = strtotime($startDate);
      $end = strtotime($endDate);

      // Arrays to collect times for totals
      $totalWorkHours = [];
      $totalTravelHours = [];
      $totalLeaveHours = [];
      $totalSickHours = [];
      $totalOvertimeHours = [];

      while ($current <= $end) {
        $date = date('Y-m-d', $current);
        $dayName = $this->getDutchDayName(date('w', $current));
        $formattedDate = date('d-m', $current);

        /** @var IndufastWorkday $workday */
        $workday = $workdaysByDate[$date] ?? null;
        if ($workday) {
          $workHours = $workday->workdayDurationNetClipped;
          $travelHours = $workday->travelDurationNet;
          $leaveHours = $workday->specialHoursLeave;
          $sickHours = $workday->specialHoursSick;

          $overtimeHours = '';
          if ($workday->overtimeBelow || $workday->overtimeAbove) {
            $overtimeParts = array_filter([$workday->overtimeBelow, $workday->overtimeAbove]);
            $overtimeHours = $overtimeParts ? $this->addTimes($overtimeParts) : '';
          }

          if ($workHours) $totalWorkHours[] = $workHours;
          if ($travelHours) $totalTravelHours[] = $travelHours;
          if ($leaveHours) $totalLeaveHours[] = $leaveHours;
          if ($sickHours) $totalSickHours[] = $sickHours;
          if ($overtimeHours) $totalOvertimeHours[] = $overtimeHours;

          $rows .= sprintf(
            '<tr style="border-bottom: 1px solid #eee;">
                      <td style="padding: 10px; border: 1px solid #ddd;">%s %s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                  </tr>',
            $dayName,
            $formattedDate,
            $workHours,
            $overtimeHours,
            $travelHours,
            $leaveHours,
            $sickHours,
          );
        }
        $current = strtotime('+1 day', $current);
      }

      // Add totals row
      $totalWorkTime = $totalWorkHours ? $this->addTimes($totalWorkHours) : '';
      $totalTravelTime = $totalTravelHours ? $this->addTimes($totalTravelHours) : '';
      $totalLeaveTime = $totalLeaveHours ? $this->addTimes($totalLeaveHours) : '';
      $totalSickTime = $totalSickHours ? $this->addTimes($totalSickHours) : '';
      $totalOvertimeTime = $totalOvertimeHours ? $this->addTimes($totalOvertimeHours) : '';

      $rows .= sprintf(
        '<tr style="border-top: 3px solid #dd0015; background-color: #f8f9fa; font-weight: bold;">
                  <td style="padding: 12px; border: 1px solid #ddd; color: #353535;">Totaal</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
              </tr>',
        $totalWorkTime,
        $totalTravelTime,
        $totalLeaveTime,
        $totalSickTime,
        $totalOvertimeTime
      );

      return $rows;
    }

    private function getDutchDayName(int $dayOfWeek): string {
      $days = ['Zondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'];
      return $days[$dayOfWeek];
    }

  }