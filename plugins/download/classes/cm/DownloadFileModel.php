<?php

  AppModel::loadBaseClass('BaseDownloadFile');

  class DownloadFileModel extends BaseDownloadFile {

    public $content; // set content model of active language on the object
    public $content_models; // set all content models on the object

    public function save(&$errors = []) {
      if (!$this->from_db || $this->insertTS == "0000-00-00 00:00:00") {
        $this->insertTS = date('Y-m-d H:i:s');
        if (isset($_SESSION['userObject'])) {
          $this->insertUser = $_SESSION['userObject']->id;
        }
      }
      $this->updateTS = date('Y-m-d H:i:s');
      if (isset($_SESSION['userObject'])) {
        $this->updateUser = $_SESSION['userObject']->id;
      }
      return parent::save($errors);
    }

    /**
     * @return DownloadFileContent
     */
    public function getContent() {
      return $this->content;
    }

    /**
     * @param DownloadFileContent $content
     */
    public function setContent($content): void {
      $this->content = $content;
    }

    /**
     * @return DownloadFileContent[]
     */
    public function getContentModels(): array {
      return $this->content_models;
    }

    /**
     * @return DownloadFileContent
     */
    public function getContentModel($language_key): DownloadFileContent {
      return $this->content_models[$language_key];
    }

    /**
     * @param DownloadFileContent $content_model
     * @param string $language_key
     */
    public function setContentModel(DownloadFileContent $content_model, string $language_key): void {
      $this->content_models[$language_key] = $content_model;
    }

    /**
     * @param int $parent_id
     * @param string $language
     * @return DownloadFile[]
     */
    public static function getWithContentByParentId(int $parent_id, string $language): array {
      $query = <<<SQL
      SELECT * FROM download_file
        JOIN download_file_content ON download_file_content.download_file_id = download_file.id
          AND download_file_content.locale = '$language'
          AND download_file_content.filelocation IS NOT NULL
      WHERE download_file.download_category_id = $parent_id
SQL;
      $result = DBConn::db_link()->query($query);
      $download_files = [];
      while ($row = $result->fetch_row()) {

        $column_counter = 0;
        $download_file = new DownloadFile()->hydrateNext($row, $column_counter);

        $download_file_content = new DownloadFileContent()->hydrateNext($row, $column_counter);
        $download_file->setContent($download_file_content);

        $download_files[] = $download_file;
      }

      return $download_files;
    }

  }