<?php

  use Gsd\Form\ModelForm;

  class stockActions extends gsdActions {

    public function executeStock() {

      $errors = [];
      $products = [];
      $category = null;
      if (!isset($this->cats))
        $this->cats = Category::find_all(" WHERE void = 0 ORDER BY sort");

      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 200;
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['st_search'])) {
        $_SESSION['st_search'] = '';
      }
      if (isset($_POST['st_search'])) {
        $_SESSION['st_search'] = trim($_POST['st_search']);
      }
      if (isset($_POST['cat'])) {
        $_GET['cat'] = $_POST['cat'];
      }

      if (isset($_POST['submitt'])) {
        if ($_POST['cat'] == '') {
          $errors[] = "Selecteer eerst een categorie";
        }
        if (count($errors) == 0) {
          ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'action']) . '&cat=' . $_POST['cat']);
        }
      }

      if ((isset($_POST['cat']) && $_POST['cat'] != '') || (isset($_GET['cat']) && $_GET['cat'] != '')) {
        if (isset($_GET['cat'])) {
          $catid = $_GET['cat'];
        }
        if (isset($_POST['cat'])) {
          $catid = $_POST['cat'];
        }
        foreach ($this->cats as $cat) {
          if ($catid == $cat->id) {
            $category = $cat;
            break;
          }
        }
        if (!$category) {
          $errors[] = "Selecteer een categorie";
        }
      }

      if (count($errors) == 0) {

        if ($category) {
          $filtquery = Product::getFilterQuery($category->id, $_SESSION['st_search'], $_SESSION['lang']);

          $query = "";
          $query .= "SELECT * FROM product" . $filtquery;
          $query .= " ORDER BY category_product.sort ";
          $query .= $this->pager->getLimitQuery();

          $result = DBConn::db_link()->query($query);
          while ($row = $result->fetch_row()) {
            $product = new Product();
            $product->hydrate($row);
            $product->from_db = true;

            $productc = new ProductContent();
            $productc->hydrate($row, count(Product::columns));
            $product->content = $productc;

            $products[] = $product;
          }

          $this->pager->count = Product::count_all_by([], $filtquery);
        }

        if (isset($_POST['go'])) {

          foreach ($products as $product) {
            $changed = false;
            if ($product->not_in_backorder != (isset($_POST['not_in_backorder'][$product->id]) ? 1 : 0)) {
              $product->not_in_backorder = isset($_POST['not_in_backorder'][$product->id]) ? 1 : 0;
              $changed = true;
            }
            if ($product->stock != $_POST['stock'][$product->id]) {
              $product->setStock($_POST['stock'][$product->id]);
              $changed = true;
            }
            if ($product->stock_level != $_POST['stock_level'][$product->id]) {
              $product->stock_level = $_POST['stock_level'][$product->id];
              $changed = true;
            }

            if ($changed) {
              $product->save();
            }
          }

          ResponseHelper::redirectMessage("Voorraad succesvol bijgewerkt", reconstructQueryAdd(['pageId', 'action']) . '&cat=' . $category->id);

        }
      }

      $this->errors = $errors;
      $this->products = $products;
      $this->category = $category;
    }

    public function executeStock60() {
      $final_products = [];
      $errors = [];
      if (!isset($this->cats)) $this->cats = Category::find_all(" WHERE void != 1 ORDER BY sort");
      $category = null;

      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 300;
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['st60_search'])) {
        $_SESSION['st60_search'] = '';
      }
      if (isset($_POST['st60_search'])) {
        $_SESSION['st60_search'] = trim($_POST['st60_search']);
      }

      if (isset($_POST['cat'])) {
        $_GET['cat'] = $_POST['cat'];
      }

      if (isset($_POST['submitt'])) {
        if ($_POST['cat'] == '') {
          $errors[] = "Selecteer eerst een categorie";
        }
        if (count($errors) == 0) {
          ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'action']) . '&cat=' . $_POST['cat']);
        }
      }

      if ((isset($_POST['cat']) && $_POST['cat'] != '') || (isset($_GET['cat']) && $_GET['cat'] != '')) {
        if (isset($_GET['cat'])) {
          $catid = $_GET['cat'];
        }
        if (isset($_POST['cat'])) {
          $catid = $_POST['cat'];
        }
        foreach ($this->cats as $cat) {
          if ($catid == $cat->id) {
            $category = $cat;
            break;
          }
        }
        if (!$category) {
          $errors[] = "Selecteer een categorie";
        }
      }

      if (count($errors) == 0) {

        if ($category) {
          $filtquery = Product::getFilterQuery($category->id, $_SESSION['st60_search'], $_SESSION['lang']);

          $query = "SELECT * FROM product " . $filtquery;
          $query .= $this->pager->getLimitQuery();
          $this->pager->count = Product::count_all_by([], $filtquery);

          $result = DBConn::db_link()->query($query);
          $products = [];
          while ($row = $result->fetch_row()) {
            $product = new Product();
            $product->hydrate($row);

            $productc = new ProductContent();
            $productc->hydrate($row, count(Product::columns));
            $product->content = $productc;

            $products[$product->id] = $product;
          }

          $stock = [];
          foreach ($products as $product) {
            $stock[$product->id] = $product->stock;
          }

          if (Config::get("STOCK_FLOW_EXPECTED_SELLS")) {

            $expectsells = Product::getSellsLast60Days($stock);

            $final_products = null;
            $ids = null;
            foreach ($expectsells as $key => $expect) {
              if (!isset($products[$key])) continue;

              $prod = $products[$key];
              $prod->sold = $expect->sold;
              $prod->expectd = $expect->expect;
              $prod->expect = $expect->expect . $expect->date;
              $final_products[] = $prod;
            }

            usort($final_products, function ($a, $b) {
              return $a->expectd - $b->expectd;
            });

          }
          else {
            $final_products = $products;
          }

        }
      }

      $this->errors = $errors;
      $this->products = $final_products;
      $this->category = $category;
    }

    public function cmp($a, $b) {
      if ($a['days'] == $b['days']) {
        return 0;
      }
      return ($a['days'] < $b['days']) ? -1 : 1;
    }

    public function executeWarehouselist() {
      $this->warehouses = Warehouse::find_all('ORDER BY name');
    }

    public function executeWarehouseedit() {
      $errors = [];

      $wh = new Warehouse();
      $warehouseboxes = [];
      if (isset($_GET['id'])) {
        $wh = Warehouse::find_by(['id' => $_GET['id']]);
        $warehouseboxes = WarehouseBox::find_all_by(['warehouse_id' => $wh->id], "ORDER BY boxname");
      }


      if (isset($_POST['verzend']) || isset($_POST['verzend_list'])) {

        $wh->name = trim($_POST['name']);

        if ($wh->name == "") {
          $errors[] = "Naam is verplicht";
        }
        if (count($errors) == 0) {
          $wh->save();

          if (isset($_POST['boxname'])) {
            $handled = [];
            $warehouseboxes = AppModel::mapObjectIds($warehouseboxes);
            foreach ($_POST['boxname'] as $id => $name) {
              if ($id == "new") { //new
                foreach ($name as $lname) {
                  if ($lname != "") {
                    $box = new WarehouseBox();
                    $box->warehouse_id = $wh->id;
                    $box->boxname = $lname;
                    $box->save();
                  }
                }
              }
              else {
                $box = $warehouseboxes[$id];
                if ($name != "") { //update
                  $box->boxname = $name;
                  $box->save();
                }
                else {
                  $box->destroy();
                }
                $handled[$id] = $id;
              }
            }

            foreach ($warehouseboxes as $id => $whb) {
              if (!isset($handled[$id])) { //verwijderd
                $whb->destroy();
              }

            }

          }

          $_SESSION['flash_message'] = "Magazijn opgeslagen";
          if (isset($_POST['verzend_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_WAREHOUSE'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_WAREHOUSE', ['action' => 'warehouseedit', 'id' => $wh->id]));
          }
        }

      }

      $this->errors = $errors;
      $this->warehouse = $wh;
      $this->warehouseboxes = $warehouseboxes;

    }

    public function executeWarehousedelete() {
      if (isset($_GET['id'])) {
        $wh = Warehouse::find_by_id($_GET['id']);

        if ($wh) {
          $wh->destroy();
          $_SESSION['flash_message'] = "Magazijn is verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }

      $_SESSION['flash_message_red'] = "Tag niet gevonden.";
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

    public function executeWarehouseproductlist() {
      $errors = [];
      $errors_light = [];

      if (!isset($_SESSION['whp_search']))
        $_SESSION['whp_search'] = '';
      if (!isset($_SESSION['whp_warehouse_box_id']))
        $_SESSION['whp_warehouse_box_id'] = '';
      if (!isset($_SESSION['whp_brand']))
        $_SESSION['whp_brand'] = '';
      if (!isset($_SESSION['whp_withstock']))
        $_SESSION['whp_withstock'] = '1';

      if (isset($_POST['whp_search'])) {
        $_SESSION['whp_search'] = trim($_POST['whp_search']);
        $_SESSION['whp_warehouse_box_id'] = $_POST['whp_warehouse_box_id'];
        $_SESSION['whp_brand'] = $_POST['whp_brand'];
        $_SESSION['whp_withstock'] = isset($_POST['whp_withstock']) ? 1 : 0;
      }

      if (!empty($_GET['whp_search'])) { // openen vanuit catalogus
        $_POST['whp_search'] = DbHelper::escape($_GET['whp_search']);
        $_SESSION['whp_search'] = trim($_POST['whp_search']);
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->setRowsPerPage(50);
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      if (isset($_POST['whp_search']) && !isset($_POST['go'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $filtquery = "JOIN product_content ON product_content.product_id = product.id AND locale='nl' ";
      if ($_SESSION['whp_brand'] == "" && $_SESSION['whp_withstock'] == 0) {
        $filtquery .= "LEFT ";
      }
      $filtquery .= "JOIN warehouse_product ON product.id=warehouse_product.product_id ";
      if ($_SESSION['whp_brand'] != "") {
        $filtquery .= " AND product.brand_id='" . $_SESSION['whp_brand'] . "' ";
      }

      $filtquery .= " WHERE product.void=0 ";
      if ($_SESSION['whp_warehouse_box_id'] != "") {
        $filtquery .= " AND warehouse_product.warehouse_box_id='" . $_SESSION['whp_warehouse_box_id'] . "' ";
      }
      $ssearch = escapeForDB($_SESSION['whp_search']);
      if ($ssearch != "") {
        $filtquery .= " AND (";
        $filtquery .= " product.code LIKE '%" . $ssearch . "%' ";
        $filtquery .= " OR product.supplier_code LIKE '%" . $ssearch . "%'";
        $filtquery .= " OR product_content.name LIKE '%" . $ssearch . "%' ";
        $filtquery .= " OR product_content.description LIKE '%" . $ssearch . "%' ";
        $filtquery .= " ) ";
      }

      $this->pager->count = Product::count_all_by([], $filtquery);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $query = "SELECT * FROM product ";
      $query .= $filtquery;
      //$query .= "ORDER BY service_price ASC";

      $query .= $this->pager->getLimitQuery();

      $result = DBConn::db_link()->query($query);
      $products = [];
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);
        $product->from_db = true;

        $productc = new ProductContent();
        if (isset($products[$product->id])) {
          $product = $products[$product->id];
        }
        else {
          $productc->hydrate($row, count(Product::columns));
          $productc->from_db = true;
          $product->content = $productc;
        }

        $whp = new WarehouseProduct();
        $whp->hydrate($row, count(Product::columns) + count(ProductContent::columns));
        if ($whp->id != null) {
          $whp->from_db = true;
        }

        $product->whproducts[] = $whp;

        $products[$product->id] = $product;
      }

      $boxnamesar = WarehouseBox::getBoxenameArray();

      if (isset($_POST['go'])) {
        //@fixme: wisselen van een voorraad vak.
        foreach ($_POST['products'] as $prodid => $prodcont) {
          $wh_ids = [];
          foreach ($prodcont['warehouse_box_id'] as $key => $warehouse_box_id) {
            if (!isset($products[$prodid]) || !isset($boxnamesar[$warehouse_box_id])) { //verwijderd product of warehousebox, skippen
              continue;
            }
            if (isset($wh_ids[$warehouse_box_id])) {
              $errors['vak'] = '2x hetzelfde voorraadvak, dit is niet moglijk.';
              $errors_light['warehouse_box_id'][$prodid] = true;
              continue;
            }
            $wh_ids[$warehouse_box_id] = $warehouse_box_id;
            $size = $prodcont['size'][$key];
            if ($size == "") $size = 0;
            //$warehouse_product_id = $prodcont['warehouse_product_id'][$key];
            if (!isset($products[$prodid]->whproducts[$key])) { //nieuw vak
              $products[$prodid]->whproducts[$key] = new WarehouseProduct();
            }
            if ($products[$prodid]->whproducts[$key]->warehouse_box_id != $warehouse_box_id) {
              $products[$prodid]->whproducts[$key]->setDirty("warehouse_box_id", $products[$prodid]->whproducts[$key]->warehouse_box_id);
            }
            if ($products[$prodid]->whproducts[$key]->size != $size) {
              $products[$prodid]->whproducts[$key]->setDirty("size", $products[$prodid]->whproducts[$key]->size);
            }

            $products[$prodid]->whproducts[$key]->warehouse_box_id = $warehouse_box_id;
            $products[$prodid]->whproducts[$key]->setSize($size);

            if ($size > 0 && $warehouse_box_id == "") {
              $errors['vak'] = 'Selecteer voor elke product op voorraad een magazijn vak.';
              $errors_light['warehouse_box_id'][$prodid] = true;
            }
          }
        }

        if (count($errors) == 0) {
          $dirtyprods = [];
          foreach ($products as $prod) {
            foreach ($prod->whproducts as $whp) {
              if ($whp->size > 0) {
                if ($whp->id == null || $whp->hasDirty('size')) { //size is veranderd
                  $dirtyprods[$prod->id] = $prod->id;
                }
                $whp->product_id = $prod->id;
                $whp->save();
              }
              elseif ($whp->id != "") { //hij staat op 0, verwijderen maar
                $dirtyprods[$prod->id] = $prod->id;
                $whp->destroy();
              }
            }
          }

          foreach ($dirtyprods as $prodi) {
            $product = $products[$prodi];
            $product->setStock(WarehouseProduct::getProductStock($prodi));
            $product->save();
          }

          ResponseHelper::redirectMessage("Voorraad succesvol bijgewerkt", reconstructQueryAdd(['pageId', 'action']));
        }

      }


      $this->errors = $errors;
      $this->errors_light = $errors_light;
      $this->products = $products;
      $this->warehouseboxes = $boxnamesar;

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.full.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

    }

    public function executeWarehouseproductlistchange() {
      $product = Product::getProductAndContent($_GET['id']);
      $this->history = WarehouseProductChange::getHistory($product->id);
      $this->employees = AppModel::mapObjectIds(User::find_all_by([], 'ORDER BY lastname'));
      $this->product = $product;
    }


    public function executeStockflow() {
      $errors = [];
      $factory_orders = null;

      if (!isset($_SESSION['st60_search'])) {
        $_SESSION['st60_search'] = '';
      }
      if (isset($_POST['st60_search'])) {
        $_SESSION['st60_search'] = trim($_POST['st60_search']);
      }
      if (!isset($_SESSION["search_facttoryorder_status"])) {
        $_SESSION['search_facttoryorder_status'] = 'to_process';
      }
      if (isset($_POST['search_facttoryorder_status'])) {
        $_SESSION['search_facttoryorder_status'] = $_POST['search_facttoryorder_status'];
      }


      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      //ophalen fact_orders
      $filt = "";
      if ($_SESSION['search_facttoryorder_status'] != '') {
        if ($_SESSION['search_facttoryorder_status'] == 'to_process') {
          $filt = "WHERE status!='done'";
        }
        else {
          $filt = "WHERE status='" . $_SESSION['search_facttoryorder_status'] . "'";
        }
      }
      $factory_orders = FactoryOrder::getFactoryOrders($filt, $this->pager->getLimitQuery());

      $this->pager->count = FactoryOrder::count_all_by([]);
      $this->factory_orders = $factory_orders;
      $this->errors = $errors;
    }

    public function executeStockflowedit() {
      //     ini_set('display_errors', true);
      //     error_reporting(E_ALL);
      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $errors = [];
      $factorder = null;
      $factprods = [];

      //pager properties
      $this->pager = new Pager();
      $this->pager->handle();

      $products = [];

      if (isset($_GET['id'])) {
        $factorder = FactoryOrder::find_by_id($_GET['id']);

        if ($factorder->status == 'new') { //status new, alle producten tonen van brand/leverancier

          $oldfactprods = AppModel::mapObjectIds(FactoryProduct::find_all_by(['factory_order_id' => $factorder->id]), 'product_id');

          $query = "SELECT *, (product.stock - product.stock_level) as rest ";
          $query .= "FROM product ";
          $query .= "JOIN product_content ON product_content.product_id = product.id AND locale='nl' ";
          $query .= "WHERE product.void=0 ";
          if (Config::get("STOCK_FLOW_USE_BRAND")) {
            $query .= "AND brand_id=" . $factorder->brand_id . " ";
          }
          if (Config::isdefined("STOCK_FLOW_USE_SUPPLIER") && Config::get("STOCK_FLOW_USE_SUPPLIER")) {
            $query .= "AND supplier_id=" . $factorder->supplier_id . " ";
          }
          $query .= "ORDER BY rest ";

          $result = DBConn::db_link()->query($query);
          while ($row = $result->fetch_array()) {
            $prod = new Product();
            $prod->hydrate($row);
            $prod->content = new ProductContent();
            $prod->content->hydrate($row, count(Product::columns));
            //$prod->category_id = $row['category_id'];
            $prod->diff = $row['rest'];
            $prod->nr_of_products = 0;
            if (isset($oldfactprods[$prod->id])) {
              $prod->nr_of_products = $oldfactprods[$prod->id]->nr_of_products;
            }
            $products[$prod->id] = $prod;
          }

        }
        else { //anders ophalen bestelling
          $products = Product::getProductsByFactOrderId($factorder, true);
        }

      }
      else {
        $factorder = new FactoryOrder();
        $factorder->status = 'new';
      }


      if (isset($_POST['go']) || isset($_POST['go_list']) || isset($_POST['go_send']) || isset($_POST['go_process'])) {

        $factorder->name = $_POST['name'];
        $factorder->mail_to = $_POST['mail_to'];
        $factorder->expected_delivery_date = $_POST['expected_delivery_date'] == "" ? null : getTSFromStr($_POST['expected_delivery_date']);
        if ($factorder->name == '') {
          $errors[] = "Voer naam in";
        }
        if (Config::get("STOCK_FLOW_USE_BRAND")) {
          $factorder->brand_id = $_POST['brand_id'];
          if ($factorder->brand_id == '') {
            $errors[] = "Selecteer merk";
          }
        }
        if (Config::isdefined("STOCK_FLOW_USE_SUPPLIER") && Config::get("STOCK_FLOW_USE_SUPPLIER")) {
          $factorder->supplier_id = $_POST['supplier_id'];
          if ($factorder->supplier_id == '') {
            $errors[] = "Selecteer leverancier";
          }
        }
        if ($factorder->status == '') {
          $errors[] = "Status";
        }
        if (!ValidationHelper::isEmail($factorder->mail_to)) {
          $errors[] = "E-mail naar is leeg of niet valide";
        }

        if (count($errors) == 0) {
          $factorder->save($errors);

          if (isset($_POST['stockflow']) && !isset($_POST['go_process']) && $factorder->status != 'done') {

            $oldfactprods = AppModel::mapObjectIds(FactoryProduct::find_all_by(['factory_order_id' => $factorder->id]), 'product_id');
            foreach ($oldfactprods as $factprod) {
              $factprod->destroy();
            }

            foreach ($_POST['stockflow'] as $key => $values) {
              if ($values['nrofprod'] != "" && $values['nrofprod'] != "0") {
                $factprod = new FactoryProduct();
                $factprod->factory_order_id = $factorder->id;
                $factprod->product_id = $key;
                $factprod->nr_of_products = $values['nrofprod'];

                if (count($errors) == 0) {
                  $factprod->save($errors);
                  $factprods[] = $factprod;
                }
              }
            }
          }
        }

        if (isset($_POST['go_send']) && count($factprods) == 0) {
          $errors[] = "U dient minimaal 1 product te bestellen.";
        }

        if (count($errors) == 0) {
          $_SESSION['flash_message'] = 'Leveranciers bestelling opgeslagen';
          if (isset($_POST['go'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'action=stockflowedit&id=' . $factorder->id);
          }
          elseif (isset($_POST['go_send'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'action=stockflowsend&id=' . $factorder->id);
          }
          elseif (isset($_POST['go_process'])) {
            $this->executeStockflowprocess($factorder);
          }
          else {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
          }
        }
      }

      $this->factorder = $factorder;
      $this->factorder->products = $products;
      $this->errors = $errors;
    }

    public function executeStockflowprocess($factorder) {

      $factproducts = AppModel::mapObjectIds(FactoryProduct::find_all_by(['factory_order_id' => $factorder->id]), 'product_id');
      foreach ($_POST['stockflow'] as $key => $values) {
        if (isset($values['nrofprodrec']) && $values['nrofprodrec'] != "") {
          $factproducts[$key]->nr_of_products_rec = $values['nrofprodrec'];
        }
        else {
          $factproducts[$key]->nr_of_products_rec = 0;
        }
      }

      $errors = [];
      $ordered = 0;
      $processed = 0;
      //get products
      $ids = [];
      foreach ($factproducts as $factprod) {
        $ids[] = $factprod->product_id;
      }
      if (count($ids) == 0) {
        ResponseHelper::redirectAlertMessage("Geen enkel product in deze bestelling?? U kunt geen bestelling zonder producten verwerken.");
      }
      $products = Product::find_all_by(['id' => $ids]);

      foreach ($products as $prod) {
        $factprod = $factproducts[$prod->id];
        if ($factprod->processed == null) $factprod->processed = 0;
        $ordered += $factprod->nr_of_products;
        $processed += $factprod->processed;
        $changed = false;

        $prod->setStockChangeOrigin('Leverancier bestelling ' . $factorder->name . ' (' . $factorder->id . ')');

        if (($factprod->processed + $factprod->nr_of_products_rec) != $factprod->nr_of_products && $factprod->processed != 0 && $factprod->nr_of_products_rec != 0) {
          //Al een deel verwerkt, nog steeds niet alles binnen.
          //verwerk het volgende deel alvast.
          $prod->addToStock($factprod->nr_of_products_rec);
          $factprod->processed += $factprod->nr_of_products_rec;
          $processed += $factprod->nr_of_products_rec;
          $changed = true;
        }
        elseif (($factprod->processed + $factprod->nr_of_products_rec) == $factprod->nr_of_products && $factprod->processed != 0 && $factprod->nr_of_products_rec != 0) {
          //Al een deel verwerkt, nu alles binnen.
          //verwerk de rest.
          $prod->addToStock($factprod->nr_of_products_rec);
          $factprod->processed += $factprod->nr_of_products_rec;
          $processed += $factprod->nr_of_products_rec;
          $changed = true;
        }
        elseif (($factprod->processed + $factprod->nr_of_products_rec) != $factprod->nr_of_products && $factprod->processed == 0 && $factprod->nr_of_products_rec != 0) {
          //Nog niets verwerkt, niet alles ontvangen.
          if ($factprod->processed + $factprod->nr_of_products_rec >= $factprod->nr_of_products) {
            $prod->addToStock($factprod->nr_of_products);
            $factprod->processed = $factprod->nr_of_products;
            $processed += $factprod->nr_of_products;
          }
          else {
            $prod->addToStock($factprod->nr_of_products_rec);
            $factprod->processed += $factprod->nr_of_products_rec;
            $processed += $factprod->nr_of_products_rec;
          }
          $changed = true;
        }
        elseif ($factprod->nr_of_products_rec == $factprod->nr_of_products && $factprod->processed == 0 && $factprod->nr_of_products_rec != 0) {
          //niets verwerkt alles is binnen.
          if ($factprod->processed + $factprod->nr_of_products_rec >= $factprod->nr_of_products) {
            $prod->addToStock($factprod->nr_of_products);
            $factprod->processed = $factprod->nr_of_products;
            $processed += $factprod->nr_of_products;
          }
          else {
            $prod->addToStock($factprod->nr_of_products_rec);
            $factprod->processed = $factprod->nr_of_products_rec;
            $processed += $factprod->nr_of_products_rec;
          }
          $changed = true;
        }
        if ($changed) {
          $factprod->save();
          $prod->save();
        }
      }

      if (count($errors) == 0) {
        $_SESSION['flash_message'] = 'Leveranciers bestelling succesvol opgeslagen en verwerkt';
      }
      if ($ordered == $processed) {
        $factorder->status = 'done';
        $factorder->save();
      }
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'action=stockflowedit&id=' . $factorder->id);
    }

    public function executeStockflowsend() {
      $errors = [];
      $factorder = null;

      if (isset($_GET['id'])) {
        $factorder = FactoryOrder::find_by_id($_GET['id']);
        $products = Product::getProductsByFactOrderId($factorder, true, true);
        $factorder->products = $products;
      }

      Mails::sendFactoryOrder($factorder);

      if ($factorder->status == 'send' || $factorder->status == 'done') {
        $_SESSION['flash_message'] = "Leveranciers bestelling opnieuw verzonden";
      }
      else {
        $_SESSION['flash_message'] = "Leveranciers bestelling verzonden";
        $factorder->status = 'send';
        $factorder->orderdate = date("Y-m-d");
      }

      $factorder->save($errors);

      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

    public function executeStockflowdelete() {
      if (!Privilege::hasRight('GLOBAL_ADMIN')) {
        ResponseHelper::redirectAccessDenied();
      }

      if (isset($_GET['id'])) {
        $factorder = FactoryOrder::find_by_id($_GET['id']);
        if ($factorder) {
          $products = FactoryProduct::find_all_by(['factory_order_id' => $factorder->id]);
          foreach ($products as $prod) {
            $prod->destroy();
          }
          $factorder->destroy();
          $_SESSION['flash_message'] = "Leveranciers bestelling is succesvol verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }
    }

    public function executeSupplierstats() {

      if (!isset($_SESSION["ss_supplier_id"])) {
        $_SESSION['stat_to'] = '';
        $_SESSION['stat_from'] = '';
        $_SESSION["ss_supplier_id"] = '';
        $_SESSION['ss_product'] = '';
      }
      if (isset($_POST['stat_to'])) $_SESSION['stat_to'] = $_POST['stat_to'];
      if (isset($_POST['stat_from'])) $_SESSION['stat_from'] = $_POST['stat_from'];
      if (isset($_POST['ss_supplier_id'])) $_SESSION['ss_supplier_id'] = $_POST['ss_supplier_id'];
      if (isset($_POST['ss_product'])) $_SESSION['ss_product'] = $_POST['ss_product'];

      $query = "";
      $query .= "SELECT *, sum(factory_product.nr_of_products) as size FROM factory_product ";
      $query .= "JOIN factory_order ON factory_product.factory_order_id=factory_order.id ";
      $query .= "JOIN product ON product.id=factory_product.product_id ";
      $query .= "JOIN product_content ON product.id=product_content.product_id AND locale='nl' ";
      $query .= "WHERE factory_order.status IN ('send','done') ";
      if ($_SESSION['ss_supplier_id'] != "") {
        $query .= "AND factory_order.supplier_id=" . $_SESSION['ss_supplier_id'] . " ";
      }
      if ($_SESSION['stat_from'] != "" && $_SESSION['stat_to'] != "") {
        $query .= " AND factory_order.orderdate >= '" . escapeForDB(getTSFromStr($_SESSION['stat_from'])) . "' AND factory_order.orderdate <= '" . escapeForDB(getTSFromStr($_SESSION['stat_to'])) . "' ";
      }
      if (!empty($_SESSION['ss_product'])) {
        $searchStr = DbHelper::escape($_SESSION['ss_product']);
        $query .= " AND ( ";
        $query .= " product_content.name LIKE '%" . $searchStr . "%' ";
        $query .= " OR product.code LIKE '%" . $searchStr . "%' ";
        $query .= " ) ";
      }

      $query .= "GROUP BY product.id ";

      $result = DBConn::db_link()->query($query);
      $fps = [];
      while ($row = $result->fetch_row()) {
        $fp = new FactoryProduct();
        $fp->hydrate($row);
        $fp->from_db = true;

        $fo = new FactoryOrder();
        $fo->hydrate($row, count(FactoryProduct::columns));
        $fp->order = $fo;

        $product = new Product();
        $product->hydrate($row, count(FactoryProduct::columns) + count(FactoryOrder::columns));
        $pc = new ProductContent();
        $pc->hydrate($row, count(FactoryProduct::columns) + count(FactoryOrder::columns) + count(Product::columns));
        $product->content = $pc;
        $fp->product = $product;

        $fp->size = $row[count(FactoryProduct::columns) + count(FactoryOrder::columns) + count(Product::columns) + count($pc::columns)];

        $fps[] = $fp;
      }

      $this->suppliers = AppModel::mapObjectIds(Organisation::find_all_by(['type' => Organisation::TYPE_LEVERANCIER], 'ORDER BY name'));
      $this->fps = $fps;

    }

    public function executeCrates() {
      $this->crates = OrderCrate::find_all('ORDER BY number');
    }

    public function executeCrateedit() {
      $crate = new OrderCrate();
      if (isset($_GET['id'])) {
        $crate = OrderCrate::find_by_id($_GET['id']);
      }

      $form = new ModelForm();

      //bouw formulier van appmodel object
      $form->buildElementsFromModel($crate);
      $form->getElement("number")->addAtribute("autocomplete", "off");
      $form->setElementsRequired(["number"]);


      //set labels
      $form->setElementsLabel([
        "number" => "Nummer",
      ], true);


      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form->setElementsAndObjectValue($_POST, $crate);

        $isValid = $form->isValid();
        if ($isValid) {
          if (OrderCrate::find_by(["number" => $crate->number], "AND id!='" . $crate->id . "'")) {
            $form->addError("Nummer is niet uniek.");
          }
        }

        if ($form->isValid()) { //all valid? If not errors will be shown via $form->getErrors();
          $crate->save();

          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_WAREHOUSE_CRATES'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_WAREHOUSE_CRATES', ['action' => 'crateedit', 'id' => $crate->id]));
          }

        }

      }

      $this->form = $form;
    }

    public function executeCratedelete() {
      if (isset($_GET['id'])) {
        $crate = OrderCrate::find_by_id($_GET['id']);

        if ($crate) {
          $crate->destroy();
          $_SESSION['flash_message'] = "Krat is verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }

      $_SESSION['flash_message_red'] = "Krat niet gevonden.";
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }


    public function executeStocklog() {

      /** FILTERS */
      $this->setStocklogFilters();

      Context::addStylesheet('/gsdfw/includes/jsscripts/jquery/datatables/datatables.min.css');
      Context::addJavascript('/gsdfw/includes/jsscripts/jquery/datatables/datatables.min.js');
    }

    protected function setStocklogFilters() {
      // prepare session value
      if (!isset($this->module_session['filters'])) $this->module_session['filters'] = [];
      // set filter object
      $this->list_filter = new ListFilter();

      $this->list_filter->addSearch('search', __('Product code...'));

      // this will set the available filters
      $this->list_filter->addDatepicker('from', __('Aanpast') . ' ', __('van'));
      $this->list_filter->addDatepicker('untill', '', __('tot en met'));

      // this will handle the post request and set default values for the filter
      $this->module_session['filters'] = $this->list_filter->handleRequest($this->module_session['filters'], $_POST);
    }

    public function executeStocklogajax() {

      /** FILTERS */
      $this->setStocklogFilters();

      /** PAGINATION */
      $starting_row = intval($_POST['start']);
      $rows_per_page = intval($_POST['length']);
      $page_number = floor($starting_row / $rows_per_page) + 1;

      /** FILTER QUERY */

      // franchisee filter, vacancy should belong to the franchisee
      $join = '';
      $filter_query = 'WHERE product.void=0 ';

      if (!empty($this->module_session['filters']['search'])) {
        $join = "JOIN product ON product.id=product_stock_change.product_id ";
        $join .= "JOIN product_content ON product.id=product_content.product_id AND locale='nl' ";
        $filter_query .= "AND ( ";
        $filter_query .= "product.code LIKE '%" . $this->module_session['filters']['search'] . "%' ";
        $filter_query .= ") ";
      }

      if (!empty($this->module_session['filters']['from'])) {
        $from_date = DateTimeHelper::convertFormat($this->module_session['filters']['from'], 'd-m-Y', 'Y-m-d');
        $filter_query .= "AND product_stock_change.insertTS >= '" . escapeForDB($from_date) . "' ";
      }
      if (!empty($this->module_session['filters']['untill'])) {
        $untill_date = DateTimeHelper::convertFormat($this->module_session['filters']['untill'], 'd-m-Y', 'Y-m-d');
        $filter_query .= "AND product_stock_change.insertTS <= '" . escapeForDB($untill_date) . "' ";
      }

      /** TOTALS */
      $total_count = ProductStockChange::count_all_by([]);
      $total_count_filtered = ProductStockChange::count_all_by([], $join . $filter_query);

      /** SORTING */
      // prepare session value
      if (!isset($this->module_session['sorting'])) $this->module_session['sorting'] = [];
      if (!isset($this->module_session['sorting']['sort'])) $this->module_session['sorting']['sort'] = '';
      if (!isset($this->module_session['sorting']['order'])) $this->module_session['sorting']['order'] = '';
      if (isset($_POST['order'][0]['column']) && isset($_POST['columns'][intval($_POST['order'][0]['column'])]['data'])) {
        $this->module_session['sorting']['sort'] = escapeForDB($_POST['columns'][intval($_POST['order'][0]['column'])]['data']);
      }
      if (isset($_POST['order'][0]['dir']) && in_array($_POST['order'][0]['dir'], ['asc', 'desc'])) {
        $this->module_session['sorting']['order'] = escapeForDB($_POST['order'][0]['dir']);
      }

      $this->sort_by = $this->module_session['sorting']['sort'];
      $this->order_by = $this->module_session['sorting']['order'];

      // default sorting
      $query_order = " ORDER BY product_stock_change.insertTS DESC";
      switch ($this->sort_by) {

        case 'send_on':
          $query_order = " ORDER BY product_stock_change.insertTS " . $this->order_by;
          break;

        case 'before':
          $query_order = " ORDER BY product_stock_change.before " . $this->order_by;
          break;

        case 'after':
          $query_order = " ORDER BY product_stock_change.after " . $this->order_by;
          break;

        case 'product_code':
          $query_order = " ORDER BY product.code " . $this->order_by;
          break;

        case 'product_name':
          $query_order = " ORDER BY product_content.name " . $this->order_by;
          break;

      }

      /** GET DATA */
      $query = "SELECT * FROM product_stock_change ";
      $query .= "JOIN product ON product.id=product_stock_change.product_id ";
      $query .= "JOIN product_content ON product.id=product_content.product_id AND locale='nl' ";
      $query .= $filter_query;
      $query .= $query_order;
      $query .= " LIMIT " . escapeForDB($starting_row) . ", " . escapeForDB($rows_per_page);

      $result = DBConn::db_link()->query($query);

      $product_stock_changes = [];
      $table_data = [];

      while ($row = $result->fetch_array()) {
        $_product_stock_change = new ProductStockChange();
        $_product_stock_change->hydrate($row);
        $product = new Product();
        $product->hydrate($row, count(ProductStockChange::columns));
        $productc = new ProductContent();
        $productc->hydrate($row, count(ProductStockChange::columns) + count(Product::columns));
        $product->content = $productc;
        $_product_stock_change->product = $product;

        $actions_html = (string)BtnHelper::getRemove(reconstructQuery(['pageId']) . 'action=stocklogdelete&id=' . $_product_stock_change->id);

        $table_data[] = [
          'product_code' => $_product_stock_change->product->code,
          'product_name' => $_product_stock_change->product->getName(),
          'before'       => $_product_stock_change->before,
          'after'        => $_product_stock_change->after,
          'diff'         => $_product_stock_change->after - $_product_stock_change->before,
          'origin'       => $_product_stock_change->origin ?? '',
          'send_on'      => DateTimeHelper::convertFormat($_product_stock_change->insertTS, 'Y-m-d H:i:s', 'd-m-Y H:i:s'),
          'actions'      => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => intval($_POST['draw']),
      ]);
    }

    public function executeStocklogdelete() {
      $success = false;

      if (!empty($_GET['id'])) {
        $product_stock_change = ProductStockChange::find_by_id($_GET['id']);

        if ($product_stock_change) {
          $product_stock_change->destroy();
          $success = true;
        }
      }

      if ($success) {
        $_SESSION['flash_message'] = "Log succesvol verwijderd";
      }
      else {
        $_SESSION['flash_message_red'] = "Log niet verwijderd. Log is reeds verwijderd of u heeft geen rechten deze te mogen verwijderen.";
      }

      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

    public function executeStockoverview() {
    }


  }